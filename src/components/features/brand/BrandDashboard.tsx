import { useState, useEffect, useRef } from "react";
import { useParams, useLocation } from "react-router-dom";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { PostCard } from "./PostCard";
import { BrandAnalytics } from "../analytics";
import { PostCardSkeleton } from "@/components/ui/skeleton";
import { NotificationCenter } from "../notifications";
import { PullToRefresh } from "@/components/ui/pull-to-refresh";
import { SearchWithSuggestions, AdvancedFilters } from "../search";
import { TrendingUp, Clock, Filter, Search, RefreshCw, SortDesc, Grid, List } from "lucide-react";
import { Post } from "@/types";
import { BrandService } from "@/services";
import { BRAND_NAMES } from "@/constants/brands";
import { useLoading } from "@/hooks/use-loading";
import { useNotifications } from "@/hooks/use-notifications";
import { useIsMobile } from "@/hooks/use-mobile";
import { useSwipe } from "@/hooks/use-swipe";
import { useSearch } from "@/hooks/use-search";
import { cn } from "@/lib/utils";

interface FilterOptions {
  platform?: string;
  sentiment?: string;
  dateRange?: string;
  engagement?: string;
  author?: string;
  hasReplies?: boolean;
}

export function BrandDashboard() {
  const { brandId } = useParams<{ brandId: string }>();
  const location = useLocation();
  const [posts, setPosts] = useState<Post[]>([]);
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('list');
  const [sortBy, setSortBy] = useState<'newest' | 'engagement' | 'priority'>('newest');
  const [filterBy, setFilterBy] = useState<'all' | 'pending' | 'replied'>('all');
  const [refreshing, setRefreshing] = useState(false);
  const [advancedFilters, setAdvancedFilters] = useState<FilterOptions>({});

  const { isLoading, executeAsync } = useLoading();
  const { showToast } = useNotifications();
  const isMobile = useIsMobile();
  const scrollContainerRef = useRef<HTMLDivElement>(null);

  // Enhanced search functionality
  const {
    query: searchQuery,
    setQuery: setSearchQuery,
    debouncedQuery,
    recentSearches,
    suggestions,
    handleSearch,
  } = useSearch();

  // Mobile swipe gestures
  const swipeRef = useSwipe({
    onSwipeLeft: () => {
      if (isMobile && viewMode === 'list') {
        setViewMode('grid');
        showToast('info', 'Switched to grid view');
      }
    },
    onSwipeRight: () => {
      if (isMobile && viewMode === 'grid') {
        setViewMode('list');
        showToast('info', 'Switched to list view');
      }
    },
  });

  const isAnalyticsPage = location.pathname.includes('/analytics');
  const isMessagesPage = location.pathname.includes('/messages');

  const loadPosts = async (showRefreshFeedback = false) => {
    if (!brandId) return;

    if (showRefreshFeedback) {
      setRefreshing(true);
    }

    await executeAsync(
      async () => {
        const brandPosts = await BrandService.fetchBrandPosts(brandId);
        setPosts(brandPosts);
        return brandPosts;
      },
      {
        successMessage: showRefreshFeedback ? "Posts refreshed successfully" : undefined,
        errorMessage: "Failed to load posts"
      }
    );

    if (showRefreshFeedback) {
      setRefreshing(false);
    }
  };

  useEffect(() => {
    loadPosts();
  }, [brandId]);

  const handleSendReply = async (postId: string, commentId: string, reply: string) => {
    await executeAsync(
      async () => {
        await BrandService.sendReply(postId, commentId, reply);
        // Update the post with sent reply
        setPosts(prevPosts =>
          prevPosts.map(post =>
            post.id === postId
              ? {
                  ...post,
                  comments: post.comments.filter(comment => comment.id !== commentId)
                }
              : post
          )
        );
      },
      {
        successMessage: "Reply sent successfully",
        errorMessage: "Failed to send reply"
      }
    );
  };

  const handleEditReply = async (postId: string, commentId: string, reply: string) => {
    await executeAsync(
      async () => {
        await BrandService.updateReply(postId, commentId, reply);
        // Update the post with edited reply
        setPosts(prevPosts =>
          prevPosts.map(post =>
            post.id === postId
              ? {
                  ...post,
                  comments: post.comments.map(comment =>
                    comment.id === commentId
                      ? { ...comment, aiReply: reply }
                      : comment
                  )
                }
              : post
          )
        );
      },
      {
        successMessage: "Reply updated successfully",
        errorMessage: "Failed to update reply"
      }
    );
  };

  // Enhanced filtering and sorting
  const filteredAndSortedPosts = posts
    .filter(post => {
      // Search filter
      if (debouncedQuery) {
        const searchLower = debouncedQuery.toLowerCase();
        return (
          post.title.toLowerCase().includes(searchLower) ||
          post.content.toLowerCase().includes(searchLower) ||
          post.comments.some(comment =>
            comment.content.toLowerCase().includes(searchLower) ||
            comment.author.toLowerCase().includes(searchLower)
          )
        );
      }
      return true;
    })
    .filter(post => {
      // Status filter
      switch (filterBy) {
        case 'pending':
          return post.comments.length > 0;
        case 'replied':
          return post.comments.length === 0;
        default:
          return true;
      }
    })
    .filter(post => {
      // Advanced filters
      if (advancedFilters.platform && advancedFilters.platform !== 'all') {
        if (post.platform.toLowerCase() !== advancedFilters.platform.toLowerCase()) {
          return false;
        }
      }

      if (advancedFilters.sentiment && advancedFilters.sentiment !== 'all') {
        const hasSentiment = post.comments.some(comment =>
          comment.sentiment === advancedFilters.sentiment
        );
        if (!hasSentiment) return false;
      }

      if (advancedFilters.author) {
        const hasAuthor = post.comments.some(comment =>
          comment.author.toLowerCase().includes(advancedFilters.author!.toLowerCase())
        );
        if (!hasAuthor) return false;
      }

      if (advancedFilters.engagement) {
        const totalEngagement = post.engagement.likes + post.engagement.comments + post.engagement.shares;
        switch (advancedFilters.engagement) {
          case 'high':
            if (totalEngagement < 100) return false;
            break;
          case 'medium':
            if (totalEngagement < 10 || totalEngagement >= 100) return false;
            break;
          case 'low':
            if (totalEngagement >= 10) return false;
            break;
        }
      }

      return true;
    })
    .sort((a, b) => {
      // Sorting
      switch (sortBy) {
        case 'engagement':
          const aEngagement = a.engagement.likes + a.engagement.comments + a.engagement.shares;
          const bEngagement = b.engagement.likes + b.engagement.comments + b.engagement.shares;
          return bEngagement - aEngagement;
        case 'priority':
          return b.comments.length - a.comments.length;
        default: // newest
          return new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime();
      }
    });

  const totalPendingReplies = posts.reduce((total, post) => total + post.comments.length, 0);

  if (!brandId) {
    return (
      <div className="flex-1 flex items-center justify-center bg-gradient-subtle">
        <div className="text-center">
          <h2 className="text-2xl font-bold text-foreground mb-2">Select a Brand</h2>
          <p className="text-muted-foreground">Choose a brand from the sidebar to view their social media posts and manage responses.</p>
        </div>
      </div>
    );
  }

  if (isAnalyticsPage) {
    return (
      <div className="flex-1 bg-gradient-subtle">
        <header className="border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60 sticky top-0 z-40">
          <div className="container mx-auto px-4 md:px-6 py-4">
            <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-4">
              <div className="flex items-center gap-4">
                <div>
                  <h1 className="text-xl font-bold text-foreground">
                    {BRAND_NAMES[brandId] || brandId} - Analytics
                  </h1>
                  <p className="text-sm text-muted-foreground">
                    AI performance metrics and brand insights
                  </p>
                </div>
              </div>

              <div className="flex items-center gap-4 flex-wrap">
                <Badge variant="outline" className="bg-primary-light border-primary/20 text-primary">
                  <TrendingUp className="w-3 h-3 mr-1" />
                  Learning Active
                </Badge>
                <NotificationCenter />
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => loadPosts(true)}
                  disabled={refreshing}
                  className={cn(refreshing && "animate-spin")}
                >
                  <RefreshCw className="w-4 h-4" />
                  {!isMobile && <span className="ml-2">Refresh</span>}
                </Button>
              </div>
            </div>
          </div>
        </header>

        <main className="container mx-auto px-6 py-8">
          <BrandAnalytics />
        </main>
      </div>
    );
  }

  return (
    <div className="flex-1 bg-gradient-subtle">
      {/* Header */}
      <header className="border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60 sticky top-0 z-40">
        <div className="container mx-auto px-6 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-4">
              <div>
                <h1 className="text-xl font-bold text-foreground">
                  {BRAND_NAMES[brandId] || brandId} - Messages
                </h1>
                <p className="text-sm text-muted-foreground">
                  {totalPendingReplies} pending replies across {posts.length} posts
                </p>
              </div>
            </div>
            
            <div className="flex items-center gap-4 flex-wrap">
              <Badge variant="outline" className="bg-primary-light border-primary/20 text-primary">
                <TrendingUp className="w-3 h-3 mr-1" />
                Learning Active
              </Badge>
              <NotificationCenter />
              <Button
                variant="outline"
                size="sm"
                onClick={() => loadPosts(true)}
                disabled={refreshing}
                className={cn(refreshing && "animate-spin")}
              >
                <RefreshCw className="w-4 h-4" />
                {!isMobile && <span className="ml-2">Refresh</span>}
              </Button>
            </div>
          </div>

          {/* Search and Filters */}
          <div className="flex flex-col sm:flex-row items-start sm:items-center gap-4 mt-4">
            <SearchWithSuggestions
              value={searchQuery}
              onChange={setSearchQuery}
              onSearch={handleSearch}
              placeholder="Search posts, comments, authors..."
              suggestions={suggestions}
              recentSearches={recentSearches}
              className="flex-1 max-w-md"
            />

            <div className="flex items-center gap-2 flex-wrap">
              {/* Sort Dropdown */}
              <select
                value={sortBy}
                onChange={(e) => setSortBy(e.target.value as any)}
                className="px-3 py-2 text-sm border rounded-md bg-background"
              >
                <option value="newest">Newest First</option>
                <option value="engagement">Most Engaged</option>
                <option value="priority">Most Replies</option>
              </select>

              {/* Filter Dropdown */}
              <select
                value={filterBy}
                onChange={(e) => setFilterBy(e.target.value as any)}
                className="px-3 py-2 text-sm border rounded-md bg-background"
              >
                <option value="all">All Posts</option>
                <option value="pending">Pending Replies</option>
                <option value="replied">Completed</option>
              </select>

              {/* Advanced Filters */}
              <AdvancedFilters
                filters={advancedFilters}
                onFiltersChange={setAdvancedFilters}
                onClearFilters={() => setAdvancedFilters({})}
              />

              {/* View Mode Toggle */}
              <div className="flex items-center border rounded-md">
                <Button
                  variant={viewMode === 'list' ? 'default' : 'ghost'}
                  size="sm"
                  onClick={() => setViewMode('list')}
                  className="rounded-r-none"
                >
                  <List className="w-4 h-4" />
                </Button>
                <Button
                  variant={viewMode === 'grid' ? 'default' : 'ghost'}
                  size="sm"
                  onClick={() => setViewMode('grid')}
                  className="rounded-l-none"
                >
                  <Grid className="w-4 h-4" />
                </Button>
              </div>
            </div>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <PullToRefresh
        onRefresh={() => loadPosts(true)}
        disabled={!isMobile || isLoading}
        className="flex-1"
      >
        <main
          className="container mx-auto px-4 md:px-6 py-8"
          ref={(el) => {
            if (scrollContainerRef.current !== el) {
              scrollContainerRef.current = el;
            }
            if (swipeRef.current !== el) {
              swipeRef.current = el;
            }
          }}
        >
          {isLoading ? (
            <div className={cn(
              "space-y-6",
              viewMode === 'grid' && "grid grid-cols-1 lg:grid-cols-2 gap-6 space-y-0"
            )}>
              {Array.from({ length: 3 }).map((_, i) => (
                <PostCardSkeleton key={i} />
              ))}
            </div>
          ) : filteredAndSortedPosts.length === 0 ? (
            <div className="text-center py-12">
              <div className="max-w-md mx-auto">
                <div className="w-16 h-16 mx-auto mb-4 bg-muted rounded-full flex items-center justify-center">
                  <Search className="w-8 h-8 text-muted-foreground" />
                </div>
                <h3 className="text-lg font-medium text-foreground mb-2">
                  {searchQuery ? 'No posts match your search' : 'No posts found'}
                </h3>
                <p className="text-muted-foreground">
                  {searchQuery
                    ? 'Try adjusting your search terms or filters.'
                    : 'No posts found for this brand. Check back later for new content.'}
                </p>
                {searchQuery && (
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => setSearchQuery('')}
                    className="mt-4"
                  >
                    Clear search
                  </Button>
                )}
                {isMobile && (
                  <p className="text-xs text-muted-foreground mt-4">
                    💡 Tip: Pull down to refresh or swipe left/right to change view
                  </p>
                )}
              </div>
            </div>
          ) : (
            <div className={cn(
              "space-y-6",
              viewMode === 'grid' && "grid grid-cols-1 lg:grid-cols-2 gap-6 space-y-0"
            )}>
              {filteredAndSortedPosts.map((post) => (
                <PostCard
                  key={post.id}
                  post={post}
                  onSendReply={handleSendReply}
                  onEditReply={handleEditReply}
                />
              ))}
            </div>
          )}
        </main>
      </PullToRefresh>
    </div>
  );
}
