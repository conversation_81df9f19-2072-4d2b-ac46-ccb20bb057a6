import { SidebarProvider, SidebarTrigger } from "@/components/ui/sidebar";
import { AppSidebar } from "@/components/features/navigation";
import { AccessibilitySettings } from "@/components/features/accessibility";
import { Menu } from "lucide-react";
import { LayoutProps } from "@/types";
import { useIsMobile } from "@/hooks/use-mobile";

export function Layout({ children }: LayoutProps) {
  const isMobile = useIsMobile();

  return (
    <SidebarProvider defaultOpen={!isMobile}>
      {/* Skip to main content link for keyboard users */}
      <a
        href="#main-content"
        className="skip-link"
        onFocus={(e) => e.target.scrollIntoView()}
      >
        Skip to main content
      </a>

      <div className="min-h-screen flex w-full">
        <AppSidebar />

        <div className="flex-1 flex flex-col">
          <main id="main-content" className="flex-1" tabIndex={-1}>
            {children}
          </main>
        </div>

        {/* Sidebar Trigger - positioned for better mobile UX */}
        <div className={`fixed ${isMobile ? 'bottom-4 left-4' : 'top-4 left-4'} z-50`}>
          <SidebarTrigger
            className="bg-background/80 backdrop-blur border shadow-sm hover:bg-background transition-all duration-200"
            aria-label="Toggle navigation menu"
          >
            <Menu className="h-4 w-4" />
          </SidebarTrigger>
        </div>

        {/* Accessibility Settings - positioned for easy access */}
        <div className={`fixed ${isMobile ? 'bottom-4 right-4' : 'top-4 right-4'} z-50`}>
          <div className="bg-background/80 backdrop-blur border shadow-sm rounded-md">
            <AccessibilitySettings />
          </div>
        </div>
      </div>
    </SidebarProvider>
  );
}
